import { NextRequest } from 'next/server'
import { getCurrentUser } from '@/lib/services/auth.service'
import { prisma } from '@/lib/database/connection-manager'
import { getTransactionError } from '@/lib/constants/api-messages'
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
} from '@/lib/api/response'
// import { getUserTranslator } from '@/lib/utils/server-i18n'
import { PAGINATION } from '@/lib/constants/app-config'
import { TransactionType, Prisma } from '@prisma/client'
import { getAllCategoryIds } from '@/lib/services/category-summary/utils'

// 注意：getDescendantCategoryIds 已替换为优化的 getAllCategoryIds 函数

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return unauthorizedResponse()
    }

    const { searchParams } = new URL(request.url)
    const accountId = searchParams.get('accountId')
    const categoryId = searchParams.get('categoryId')
    const currencyId = searchParams.get('currencyId')
    const type = searchParams.get('type') as TransactionType | null
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const search = searchParams.get('search')
    const tagIds = searchParams.get('tagIds')?.split(',').filter(Boolean) || []
    const excludeBalanceAdjustment =
      searchParams.get('excludeBalanceAdjustment') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(
      searchParams.get('limit') || PAGINATION.DEFAULT_PAGE_SIZE.toString()
    )
    const skip = (page - 1) * limit

    // 构建基础查询条件
    const baseConditions: Prisma.TransactionWhereInput[] = [{ userId: user.id }]

    // 根据参数决定是否排除余额调整类型的交易
    if (excludeBalanceAdjustment) {
      baseConditions.push({ type: { not: 'BALANCE' } })
    }

    if (accountId) {
      baseConditions.push({ accountId })
    }

    if (categoryId) {
      // 使用优化的CTE版本获取所有子分类ID
      const allCategoryIds = await getAllCategoryIds(prisma, categoryId)
      baseConditions.push({
        account: {
          categoryId: { in: allCategoryIds },
        },
      })
    }

    if (currencyId) {
      baseConditions.push({ currencyId })
    }

    if (type) {
      if (excludeBalanceAdjustment) {
        // 如果排除余额调整记录，需要同时排除BALANCE
        baseConditions.push({
          type: {
            equals: type,
            not: 'BALANCE',
          },
        })
      } else {
        // 如果不排除余额调整记录，直接按类型过滤
        baseConditions.push({ type })
      }
    }

    // 日期范围过滤
    if (dateFrom || dateTo) {
      const dateCondition: Record<string, Date> = {}
      if (dateFrom) {
        dateCondition.gte = new Date(dateFrom)
      }
      if (dateTo) {
        const endDate = new Date(dateTo)
        endDate.setHours(23, 59, 59, 999) // 包含整天
        dateCondition.lte = endDate
      }
      baseConditions.push({ date: dateCondition })
    }

    // 标签筛选
    if (tagIds.length > 0) {
      baseConditions.push({
        tags: {
          some: {
            tagId: {
              in: tagIds,
            },
          },
        },
      })
    }

    // 构建最终查询条件
    let where: Prisma.TransactionWhereInput

    if (search) {
      // 当有搜索条件时，需要将搜索条件与其他条件正确组合
      const allConditions: Prisma.TransactionWhereInput[] = [
        ...baseConditions,
        {
          OR: [
            { description: { contains: search } },
            { notes: { contains: search } },
          ],
        },
      ]

      where =
        allConditions.length === 1 && allConditions[0]
          ? allConditions[0]
          : { AND: allConditions }
    } else {
      // 没有搜索条件时，直接使用条件
      where =
        baseConditions.length === 1 && baseConditions[0]
          ? baseConditions[0]
          : { AND: baseConditions }
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        include: {
          account: {
            include: {
              category: true,
            },
          },
          currency: true,
          tags: {
            include: {
              tag: true,
            },
          },
        },
        orderBy: [{ date: 'desc' }, { updatedAt: 'desc' }],
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where }),
    ])

    // 格式化交易数据，移除标签颜色信息
    const formattedTransactions = transactions.map(transaction => ({
      ...transaction,
      // 分类信息现在通过账户获取
      category: {
        id: transaction.account.category.id,
        name: transaction.account.category.name,
        type: transaction.account.category.type,
      },
      tags: transaction.tags.map(tt => ({
        tag: {
          id: tt.tag.id,
          name: tt.tag.name,
        },
      })),
    }))

    return successResponse({
      transactions: formattedTransactions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Get transactions error:', error)
    return errorResponse('获取交易记录失败', 500)
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return unauthorizedResponse()
    }

    const body = await request.json()

    const {
      accountId,
      currencyCode,
      type,
      amount,
      description,
      notes,
      date,
      tagIds = [],
    } = body

    // 验证必填字段
    if (
      !accountId ||
      !currencyCode ||
      !type ||
      !amount ||
      !description ||
      !date
    ) {
      return errorResponse('请填写所有必填字段', 400)
    }

    // 验证金额
    // BALANCE类型交易允许为0（如贷款还完时余额为0），其他类型必须大于0
    const amountValue = parseFloat(amount)
    if (
      isNaN(amountValue) ||
      (type === 'BALANCE' ? amountValue < 0 : amountValue <= 0)
    ) {
      return errorResponse(
        type === 'BALANCE' ? '余额调整金额不能为负数' : '金额必须是大于0的数字',
        400
      )
    }

    // 验证账户是否属于当前用户
    const account = await prisma.account.findFirst({
      where: { id: accountId, userId: user.id },
      include: {
        category: true,
        currency: true,
      },
    })

    if (!account) {
      return errorResponse('账户不存在', 400)
    }

    // 验证账户类型与交易类型的匹配性
    const accountType = account.category.type

    if (accountType) {
      // 存量类账户（资产/负债）严格禁止创建普通交易
      if (accountType === 'ASSET' || accountType === 'LIABILITY') {
        console.error(
          'Validation failed: Stock account cannot have regular transactions'
        )
        return errorResponse(
          `存量类账户"${account.name}"不能直接添加交易记录。请使用"更新余额"功能来管理${accountType === 'ASSET' ? '资产' : '负债'}账户的余额变化。`,
          400
        )
      }

      // 流量类账户（收入/支出）的严格验证
      if (accountType === 'INCOME' && type !== 'INCOME') {
        console.error(
          'Validation failed: Income account with non-income transaction',
          {
            accountName: account.name,
            accountType,
            transactionType: type,
            expectedType: 'INCOME',
          }
        )
        return errorResponse(
          '收入类账户只能记录收入交易，请选择正确的交易类型',
          400
        )
      }

      if (accountType === 'EXPENSE' && type !== 'EXPENSE') {
        console.error(
          'Validation failed: Expense account with non-expense transaction',
          {
            accountName: account.name,
            accountType,
            transactionType: type,
            expectedType: 'EXPENSE',
          }
        )
        return errorResponse(
          '支出类账户只能记录支出交易，请选择正确的交易类型',
          400
        )
      }

      // 禁止在普通交易中使用BALANCE类型
      if (type === 'BALANCE') {
        console.error('Validation failed: BALANCE type in regular transaction')
        return errorResponse('BALANCE类型只能通过余额更新功能使用', 400)
      }
    }

    // 验证账户货币限制
    if (account.currency && account.currency.code !== currencyCode) {
      return errorResponse(
        `此账户只能使用 ${account.currency?.name} (${account.currency.code})，无法使用 ${currencyCode}`,
        400
      )
    }

    // 验证币种（优先查找用户自定义货币）
    const currency = await prisma.currency.findFirst({
      where: {
        code: currencyCode,
        OR: [
          { createdBy: user.id }, // 用户自定义货币
          { createdBy: null }, // 全局货币
        ],
      },
      orderBy: { createdBy: 'desc' }, // 用户自定义货币优先
    })

    if (!currency) {
      return errorResponse('币种不存在', 400)
    }

    // 创建交易
    const transaction = await prisma.transaction.create({
      data: {
        userId: user.id,
        accountId,
        currencyId: currency.id,
        type: type as TransactionType,
        amount: parseFloat(amount),
        description,
        notes: notes || null,
        date: new Date(date),
        tags: {
          create: tagIds.map((tagId: string) => ({
            tagId,
          })),
        },
      },
      include: {
        account: {
          include: {
            category: true,
          },
        },
        currency: true,
        tags: {
          include: {
            tag: true,
          },
        },
      },
    })

    // 格式化交易数据，移除标签颜色信息
    const formattedTransaction = {
      ...transaction,
      // 分类信息现在通过账户获取
      category: {
        id: transaction.account.category.id,
        name: transaction.account.category.name,
        type: transaction.account.category.type,
      },
      tags: transaction.tags.map(tt => ({
        tag: {
          id: tt.tag.id,
          name: tt.tag.name,
        },
      })),
    }

    return successResponse(formattedTransaction, '交易创建成功')
  } catch (error) {
    console.error('Create transaction error:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type',
    })
    return errorResponse(getTransactionError('CREATE_FAILED'), 500)
  }
}
