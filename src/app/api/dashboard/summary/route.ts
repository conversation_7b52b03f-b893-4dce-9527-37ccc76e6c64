import { getCurrentUser } from '@/lib/services/auth.service'
import { prisma } from '@/lib/database/connection-manager'
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
} from '@/lib/api/response'
import { getCommonError } from '@/lib/constants/api-messages'
import {
  calculateTotalBalanceWithConversion,
  calculateAccountBalance,
  validateAccountTypes,
} from '@/lib/services/account.service'
import {
  getCachedUserSettings,
  getCachedUserAccounts,
  getCachedMultipleCurrencyConversions,
  getCachedUserCategories,
} from '@/lib/services/cache.service'
import { TransactionType, AccountType } from '@/types/core/constants'
import { getDaysAgoDateRange } from '@/lib/utils/date-range'
import type { ByCurrencyInfo } from '@/types/core'

export async function GET() {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return unauthorizedResponse()
    }

    // 获取用户设置以确定本位币
    const userSettings = await getCachedUserSettings(user.id)

    const baseCurrency = userSettings?.baseCurrency || {
      code: 'CNY',
      symbol: '¥',
      name: '人民币',
    }

    // 获取账户余额汇总
    const accounts = await getCachedUserAccounts(user.id)

    // 获取所有账户的交易记录
    const allTransactions = await prisma.transaction.findMany({
      where: {
        userId: user.id,
        accountId: {
          in: accounts.map(a => a.id),
        },
      },
      include: {
        currency: true,
      },
    })

    // 将交易记录按 accountId 分组
    const transactionsByAccountId = allTransactions.reduce((acc, t) => {
      if (!acc[t.accountId]) {
        acc[t.accountId] = []
      }
      acc[t.accountId].push(t)
      return acc
    }, {} as Record<string, typeof allTransactions>)


    // 转换账户数据格式
    const accountsForCalculation = accounts.map(account => ({
      id: account.id,
      name: account.name,
      category: account.category
        ? {
            id: account.category.id,
            name: account.category.name,
            type: account.category.type as AccountType | undefined,
          }
        : {
            id: 'unknown',
            name: 'Unknown',
            type: undefined,
          },
      currency: account.currency
        ? {
            code: account.currency.code,
            symbol: account.currency.symbol,
            name: account.currency.name,
          }
        : undefined,
      transactions: (transactionsByAccountId[account.id] || []).map(t => ({
        id: t.id,
        type: t.type as TransactionType,
        amount: parseFloat(t.amount.toString()),
        date: t.date.toISOString(),
        description: t.description,
        notes: t.notes,
        currency: {
          code: t.currency.code,
          symbol: t.currency.symbol,
          name: t.currency.name,
        },
      })),
    }))

    // 分离存量类账户和流量类账户
    const stockAccounts = accountsForCalculation.filter(
      account =>
        account.category?.type === AccountType.ASSET ||
        account.category?.type === AccountType.LIABILITY
    )
    const flowAccounts = accountsForCalculation.filter(
      account =>
        account.category?.type === AccountType.INCOME ||
        account.category?.type === AccountType.EXPENSE
    )

    // 获取当前日期，确保不包含未来的交易记录
    const now = new Date()

    // 计算净资产（只包含存量类账户）
    // const _totalBalanceResult = await calculateTotalBalanceWithConversion(
    //   user.id,
    //   stockAccounts,
    //   baseCurrency,
    //   { asOfDate: now }
    // )

    // 计算各账户余额（包含转换信息）
    const accountBalances = []

    // 计算存量类账户余额（当前时点，截止到当前日期）
    for (const account of stockAccounts) {
      const balances = calculateAccountBalance(account, {
        asOfDate: now,
      })

      // 只显示有余额的账户
      const hasBalance = Object.values(balances).some(
        balance => Math.abs(balance.amount) > 0.01
      )
      if (hasBalance) {
        const balancesRecord: Record<string, number> = {}
        Object.values(balances).forEach(balance => {
          balancesRecord[balance.currencyCode] = balance.amount
        })

        accountBalances.push({
          id: account.id,
          name: account.name,
          category: account.category,
          balances: balancesRecord,
        })
      }
    }

    // 计算流量类账户余额（当前月份期间，但不超过当前日期）
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const periodEnd = new Date(
      Math.min(
        now.getTime(),
        new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999
        ).getTime()
      )
    )

    for (const account of flowAccounts) {
      const balances = calculateAccountBalance(account, {
        asOfDate: now, // 添加截止日期，确保不包含未来交易
        periodStart,
        periodEnd,
        usePeriodCalculation: true,
      })

      // 只显示有余额的账户
      const hasBalance = Object.values(balances).some(
        balance => Math.abs(balance.amount) > 0.01
      )
      if (hasBalance) {
        const balancesRecord: Record<string, number> = {}
        Object.values(balances).forEach(balance => {
          balancesRecord[balance.currencyCode] = balance.amount
        })

        accountBalances.push({
          id: account.id,
          name: account.name,
          category: account.category,
          balances: balancesRecord,
        })
      }
    }

    // 获取最早的交易记录以计算记账天数
    const earliestTransaction = allTransactions.length > 0
      ? allTransactions.reduce((earliest, current) =>
          new Date(current.date) < new Date(earliest.date) ? current : earliest
        )
      : null

    // 计算记账天数
    let accountingDays = 1 // 默认显示第1天
    if (earliestTransaction) {
      const earliestDate = new Date(earliestTransaction.date)
      const today = new Date()
      // 计算天数差，包含开始日期
      const timeDiff = today.getTime() - earliestDate.getTime()
      accountingDays = Math.floor(timeDiff / (1000 * 3600 * 24)) + 1
    }




    // 计算近期收支统计（最近30天）
    const { startDate: thirtyDaysAgo, endDate: nowEndOfDay } =
      getDaysAgoDateRange(30)

    const recentActivity = allTransactions.filter(t => {
      const transactionDate = new Date(t.date)
      return (
        transactionDate >= thirtyDaysAgo &&
        transactionDate <= nowEndOfDay &&
        (t.type === TransactionType.INCOME || t.type === TransactionType.EXPENSE)
      )
    })

    // 按币种统计收支
    const activitySummary: Record<string, { income: number; expense: number }> =
      {}
    let totalIncomeInBaseCurrency = 0
    let totalExpenseInBaseCurrency = 0

    // 准备转换数据和分货币统计
    const incomeAmounts: Array<{ amount: number; currency: string }> = []
    const expenseAmounts: Array<{ amount: number; currency: string }> = []

    type CurrencyInfo = { code: string; symbol: string; name: string }
    const incomeByCurrencyRaw: Record<string, { amount: number; currency: CurrencyInfo }> = {}
    const expenseByCurrencyRaw: Record<string, { amount: number; currency: CurrencyInfo }> = {}

    recentActivity.forEach(transaction => {
      const currencyCode = transaction.currency.code
      if (!activitySummary[currencyCode]) {
        activitySummary[currencyCode] = { income: 0, expense: 0 }
      }

      const amount = parseFloat(transaction.amount.toString())
      // const accountType = transaction.account.category.type // 旧逻辑：根据账户类别类型

      // 根据交易类型判断是收入还是支出 (与 personal-cash-flow API 的核心统计逻辑保持一致)
      if (transaction.type === TransactionType.INCOME) {
        activitySummary[currencyCode].income += amount
        incomeAmounts.push({ amount, currency: currencyCode })

        // 收集收入分货币数据
        if (!incomeByCurrencyRaw[currencyCode]) {
          incomeByCurrencyRaw[currencyCode] = {
            amount: 0,
            currency: {
              code: transaction.currency.code,
              symbol: transaction.currency.symbol,
              name: transaction.currency.name
            }
          }
        }
        incomeByCurrencyRaw[currencyCode].amount += amount
      } else if (transaction.type === TransactionType.EXPENSE) {
        activitySummary[currencyCode].expense += amount
        expenseAmounts.push({ amount, currency: currencyCode })

        // 收集支出分货币数据
        if (!expenseByCurrencyRaw[currencyCode]) {
          expenseByCurrencyRaw[currencyCode] = {
            amount: 0,
            currency: {
              code: transaction.currency.code,
              symbol: transaction.currency.symbol,
              name: transaction.currency.name
            }
          }
        }
        expenseByCurrencyRaw[currencyCode].amount += amount
      }
    })

    // 转换收支到本位币
    interface ConversionResult {
      originalAmount: number
      originalCurrency: string
      convertedAmount: number
      targetCurrency: string
      rate?: number
      success: boolean
      errorMessage?: string
    }

    let incomeConversions: ConversionResult[] = []
    let expenseConversions: ConversionResult[] = []

    try {
      const [incomeConversionResults, expenseConversionResults] = await Promise.all([
        getCachedMultipleCurrencyConversions(
          user.id,
          incomeAmounts,
          baseCurrency.code
        ),
        getCachedMultipleCurrencyConversions(
          user.id,
          expenseAmounts,
          baseCurrency.code
        ),
      ])

      incomeConversions = incomeConversionResults
      expenseConversions = expenseConversionResults

      totalIncomeInBaseCurrency = incomeConversions.reduce(
        (sum, result) =>
          sum +
          (result.success ? result.convertedAmount : result.originalAmount),
        0
      )

      totalExpenseInBaseCurrency = expenseConversions.reduce(
        (sum, result) =>
          sum +
          (result.success ? result.convertedAmount : result.originalAmount),
        0
      )
    } catch (error) {
      console.error('转换收支金额失败:', error)
      // 转换失败时使用原始金额作为近似值
      totalIncomeInBaseCurrency = Object.values(activitySummary).reduce(
        (sum, activity) => sum + activity.income,
        0
      )
      totalExpenseInBaseCurrency = Object.values(activitySummary).reduce(
        (sum, activity) => sum + activity.expense,
        0
      )
    }

    // 计算总资产和总负债（本位币）
    const assetAccountsForTotal = stockAccounts.filter(
      account => account.category?.type === AccountType.ASSET
    )
    const liabilityAccountsForTotal = stockAccounts.filter(
      account => account.category?.type === AccountType.LIABILITY
    )

    const totalAssetsResult = await calculateTotalBalanceWithConversion(
      user.id,
      assetAccountsForTotal,
      baseCurrency,
      { asOfDate: now, includeAllUserCurrencies: true }
    )

    const totalLiabilitiesResult = await calculateTotalBalanceWithConversion(
      user.id,
      liabilityAccountsForTotal,
      baseCurrency,
      { asOfDate: now, includeAllUserCurrencies: true }
    )

    // 验证账户类型设置
    const validation = validateAccountTypes(accountsForCalculation)

    // 计算净资产 = 总资产 - 总负债
    const netWorthAmount =
      totalAssetsResult.totalInBaseCurrency -
      totalLiabilitiesResult.totalInBaseCurrency
    const netWorthHasErrors =
      totalAssetsResult.hasConversionErrors ||
      totalLiabilitiesResult.hasConversionErrors

    // 合并资产和负债的原币种余额
    const combinedByCurrency: Record<
      string,
      {
        currencyCode: string
        amount: number
        currency: { code: string; symbol: string; name: string }
      }
    > = {}

    // 添加资产余额（正数）
    Object.entries(totalAssetsResult.totalsByOriginalCurrency).forEach(
      ([currency, balance]) => {
        combinedByCurrency[currency] = {
          currencyCode: currency,
          amount: balance.amount,
          currency: balance.currency,
        }
      }
    )

    // 减去负债余额（从净资产角度）
    Object.entries(totalLiabilitiesResult.totalsByOriginalCurrency).forEach(
      ([currency, balance]) => {
        if (combinedByCurrency[currency]) {
          combinedByCurrency[currency].amount -= balance.amount
        } else {
          combinedByCurrency[currency] = {
            currencyCode: currency,
            amount: -balance.amount,
            currency: balance.currency,
          }
        }
      }
    )

    // 构建净资产分货币数据（使用ByCurrencyInfo结构）
    const netWorthByCurrency: Record<string, ByCurrencyInfo> = {}
    Object.entries(combinedByCurrency).forEach(([currencyCode, info]) => {
      // 查找转换详情（优先从资产转换中查找，再从负债转换中查找）
      const assetConversion = totalAssetsResult.conversionDetails.find(
        detail => detail.fromCurrency === currencyCode
      )
      const liabilityConversion = totalLiabilitiesResult.conversionDetails.find(
        detail => detail.fromCurrency === currencyCode
      )

      const conversion = assetConversion || liabilityConversion

      netWorthByCurrency[currencyCode] = {
        originalAmount: info.amount,
        convertedAmount: conversion?.convertedAmount ?
          (assetConversion?.convertedAmount || 0) - (liabilityConversion?.convertedAmount || 0) :
          info.amount,
        currency: info.currency,
        exchangeRate: conversion?.exchangeRate || 1,
        accountCount: 0, // 净资产不按账户计算
        success: conversion?.success ?? true,
      }
    })

    // 构建资产的 byCurrency 信息
    const assetsByCurrency: Record<
      string,
      {
        originalAmount: number
        convertedAmount: number
        currency: { code: string; symbol: string; name: string }
        exchangeRate: number
        accountCount: number
        success: boolean
      }
    > = {}

    // 统计每个币种的资产账户数量（包括余额为0的账户）
    const assetAccountCountByCurrency: Record<string, number> = {}
    assetAccountsForTotal.forEach(account => {
      // 直接使用账户的币种，不管是否有余额
      const currencyCode = account.currency?.code
      if (currencyCode) {
        assetAccountCountByCurrency[currencyCode] =
          (assetAccountCountByCurrency[currencyCode] || 0) + 1
      }
    })

    // 填充资产的 byCurrency 数据
    const assetsCurrencyEntries = Object.entries(
      totalAssetsResult.totalsByOriginalCurrency
    )
      .map(([currencyCode, balance]) => {
        // 查找对应的转换详情
        const conversionDetail = totalAssetsResult.conversionDetails.find(
          detail => detail.fromCurrency === currencyCode
        )

        return {
          currencyCode,
          data: {
            originalAmount: balance.amount,
            convertedAmount:
              conversionDetail?.convertedAmount || balance.amount,
            currency: balance.currency,
            exchangeRate: conversionDetail?.exchangeRate || 1,
            accountCount: assetAccountCountByCurrency[currencyCode] || 0,
            success: conversionDetail?.success ?? true,
          },
        }
      })
      // 按本币汇总金额从大到小排序
      .sort(
        (a, b) =>
          Math.abs(b.data.convertedAmount) - Math.abs(a.data.convertedAmount)
      )

    // 按排序后的顺序填充数据
    assetsCurrencyEntries.forEach(({ currencyCode, data }) => {
      assetsByCurrency[currencyCode] = data
    })

    // 构建负债的 byCurrency 信息
    const liabilitiesByCurrency: Record<
      string,
      {
        originalAmount: number
        convertedAmount: number
        currency: { code: string; symbol: string; name: string }
        exchangeRate: number
        accountCount: number
        success: boolean
      }
    > = {}

    // 统计每个币种的负债账户数量（包括余额为0的账户）
    const liabilityAccountCountByCurrency: Record<string, number> = {}
    liabilityAccountsForTotal.forEach(account => {
      // 直接使用账户的币种，不管是否有余额
      const currencyCode = account.currency?.code
      if (currencyCode) {
        liabilityAccountCountByCurrency[currencyCode] =
          (liabilityAccountCountByCurrency[currencyCode] || 0) + 1
      }
    })

    // 填充负债的 byCurrency 数据
    const liabilitiesCurrencyEntries = Object.entries(
      totalLiabilitiesResult.totalsByOriginalCurrency
    )
      .map(([currencyCode, balance]) => {
        // 查找对应的转换详情
        const conversionDetail = totalLiabilitiesResult.conversionDetails.find(
          detail => detail.fromCurrency === currencyCode
        )

        return {
          currencyCode,
          data: {
            originalAmount: balance.amount,
            convertedAmount:
              conversionDetail?.convertedAmount || balance.amount,
            currency: balance.currency,
            exchangeRate: conversionDetail?.exchangeRate || 1,
            accountCount: liabilityAccountCountByCurrency[currencyCode] || 0,
            success: conversionDetail?.success ?? true,
          },
        }
      })
      // 按本币汇总金额从大到小排序
      .sort(
        (a, b) =>
          Math.abs(b.data.convertedAmount) - Math.abs(a.data.convertedAmount)
      )

    // 按排序后的顺序填充数据
    liabilitiesCurrencyEntries.forEach(({ currencyCode, data }) => {
      liabilitiesByCurrency[currencyCode] = data
    })

    // 构建收入分货币数据
    const incomeByCurrency: Record<string, ByCurrencyInfo> = {}
    Object.entries(incomeByCurrencyRaw).forEach(([currencyCode, info]) => {
      // 查找对应的转换结果
      const conversionResult = incomeConversions.find(
        result => result.originalCurrency === currencyCode
      )

      incomeByCurrency[currencyCode] = {
        originalAmount: info.amount,
        convertedAmount: conversionResult?.convertedAmount || info.amount,
        currency: info.currency,
        exchangeRate: conversionResult?.rate || 1,
        accountCount: 0, // 收入支出不按账户计算
        success: conversionResult?.success ?? true,
      }
    })

    // 构建支出分货币数据
    const expenseByCurrency: Record<string, ByCurrencyInfo> = {}
    Object.entries(expenseByCurrencyRaw).forEach(([currencyCode, info]) => {
      // 查找对应的转换结果
      const conversionResult = expenseConversions.find(
        result => result.originalCurrency === currencyCode
      )

      expenseByCurrency[currencyCode] = {
        originalAmount: info.amount,
        convertedAmount: conversionResult?.convertedAmount || info.amount,
        currency: info.currency,
        exchangeRate: conversionResult?.rate || 1,
        accountCount: 0, // 收入支出不按账户计算
        success: conversionResult?.success ?? true,
      }
    })

    // 构建净收入分货币数据（收入 - 支出）
    const netByCurrency: Record<string, ByCurrencyInfo> = {}
    const allCurrencies = new Set([
      ...Object.keys(incomeByCurrency),
      ...Object.keys(expenseByCurrency)
    ])

    allCurrencies.forEach(currencyCode => {
      const incomeInfo = incomeByCurrency[currencyCode]
      const expenseInfo = expenseByCurrency[currencyCode]

      const originalAmount = (incomeInfo?.originalAmount || 0) - (expenseInfo?.originalAmount || 0)
      const convertedAmount = (incomeInfo?.convertedAmount || 0) -
        (expenseInfo?.convertedAmount || 0)

      // 使用收入或支出的货币信息（优先收入）
      const currency = incomeInfo?.currency || expenseInfo?.currency
      const success = (incomeInfo?.success ?? true) && (expenseInfo?.success ?? true)

      if (currency) {
        netByCurrency[currencyCode] = {
          originalAmount,
          convertedAmount,
          currency,
          exchangeRate: incomeInfo?.exchangeRate || expenseInfo?.exchangeRate || 1,
          accountCount: 0,
          success,
        }
      }
    })

    return successResponse({
      netWorth: {
        amount: netWorthAmount,
        currency: baseCurrency,
        byCurrency: netWorthByCurrency,
        hasConversionErrors: netWorthHasErrors,
      },
      totalAssets: {
        amount: totalAssetsResult.totalInBaseCurrency,
        currency: baseCurrency,
        accountCount: assetAccountsForTotal.length,
        hasConversionErrors: totalAssetsResult.hasConversionErrors,
        byCurrency: assetsByCurrency,
      },
      totalLiabilities: {
        amount: totalLiabilitiesResult.totalInBaseCurrency,
        currency: baseCurrency,
        accountCount: liabilityAccountsForTotal.length,
        hasConversionErrors: totalLiabilitiesResult.hasConversionErrors,
        byCurrency: liabilitiesByCurrency,
      },
      accountBalances,
      recentActivity: {
        summary: activitySummary,
        summaryInBaseCurrency: {
          income: totalIncomeInBaseCurrency,
          expense: totalExpenseInBaseCurrency,
          net: totalIncomeInBaseCurrency - totalExpenseInBaseCurrency,
        },
        incomeByCurrency,
        expenseByCurrency,
        netByCurrency,
        period: 30,
        baseCurrency,
      },
      stats: {
        totalAccounts: accounts.length,
        totalTransactions: allTransactions.length,
        totalCategories: (await getCachedUserCategories(user.id)).length,
        accountingDays,
      },
      validation,
    })
  } catch (error) {
    console.error('Get dashboard summary error:', error)
    return errorResponse(getCommonError('INTERNAL_ERROR'), 500)
  }
}
